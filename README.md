# Baostock股票信息查询MCP服务器

基于Baostock Python API开发的股票信息查询MCP服务器，提供股票历史数据、实时行情、财务数据等查询功能。

## 功能特性

- 📈 股票历史K线数据查询
- 📊 股票基本信息查询
- 🔍 股票搜索功能
- 💰 股票财务数据查询
- ⚡ 实时行情数据查询

## 安装

1. 克隆或下载本项目
2. 安装依赖：
```bash
pip install -r requirements.txt
```

## 使用

### 直接运行
```bash
python stock_mcp_server.py
```

### 作为MCP服务器使用

在您的MCP客户端配置中添加：

```json
{
  "mcpServers": {
    "baostock": {
      "command": "python",
      "args": ["/path/to/stock_mcp_server.py"]
    }
  }
}
```

## 可用工具

### 1. get_stock_history
获取股票历史K线数据

**参数：**
- `stock_code` (str): 股票代码，如 "sh.600000"
- `start_date` (str, optional): 开始日期，格式: YYYY-MM-DD
- `end_date` (str, optional): 结束日期，格式: YYYY-MM-DD
- `frequency` (str, optional): 数据频率: "d"=日线, "w"=周线, "m"=月线，默认"d"

### 2. get_stock_info
获取股票基本信息

**参数：**
- `stock_code` (str): 股票代码，如 "sh.600000"

### 3. search_stocks
搜索股票

**参数：**
- `keyword` (str): 搜索关键词，可以是股票代码或名称

### 4. get_financial_data
获取股票财务数据

**参数：**
- `stock_code` (str): 股票代码，如 "sh.600000"

### 5. get_realtime_quotes
获取股票实时行情

**参数：**
- `stock_code` (str): 股票代码，如 "sh.600000"

## 示例使用

### 查询股票历史数据
```json
{
  "tool": "get_stock_history",
  "arguments": {
    "stock_code": "sh.600000",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }
}
```

### 搜索股票
```json
{
  "tool": "search_stocks",
  "arguments": {
    "keyword": "浦发银行"
  }
}
```

### 获取股票基本信息
```json
{
  "tool": "get_stock_info",
  "arguments": {
    "stock_code": "sh.600000"
  }
}
```

## 注意事项

- 需要网络连接以访问Baostock服务
- 股票代码格式为 "交易所.代码"，如上交所为 "sh.xxxxxx"，深交所为 "sz.xxxxxx"
- 数据来源于Baostock，免费使用但请注意使用限制

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. Baostock服务是否可用
3. 股票代码格式是否正确
4. 日期格式是否符合要求