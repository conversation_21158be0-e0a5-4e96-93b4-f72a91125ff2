#!/usr/bin/env python3
"""
简化版Baostock股票信息查询MCP服务器
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
import baostock as bs
import mcp.types as types
from mcp.server import Server
import mcp.server.stdio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

server = Server("baostock-stock")

def login_baostock():
    lg = bs.login()
    if lg.error_code != '0':
        raise Exception(f"登录失败: {lg.error_msg}")
    return True

def logout_baostock():
    try:
        bs.logout()
    except:
        pass

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    return [
        types.Tool(
            name="get_stock_history",
            description="获取股票历史K线数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_code": {"type": "string"},
                    "start_date": {"type": "string"},
                    "end_date": {"type": "string"},
                    "frequency": {"type": "string", "default": "d"}
                },
                "required": ["stock_code"]
            }
        ),
        types.Tool(
            name="get_stock_info",
            description="获取股票基本信息",
            inputSchema={
                "type": "object",
                "properties": {"stock_code": {"type": "string"}},
                "required": ["stock_code"]
            }
        ),
        types.Tool(
            name="search_stocks",
            description="搜索股票",
            inputSchema={
                "type": "object",
                "properties": {"keyword": {"type": "string"}},
                "required": ["keyword"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    try:
        if name == "get_stock_history":
            return await get_stock_history(**arguments)
        elif name == "get_stock_info":
            return await get_stock_info(**arguments)
        elif name == "search_stocks":
            return await search_stocks(**arguments)
        else:
            raise ValueError(f"未知工具: {name}")
    except Exception as e:
        return [types.TextContent(type="text", text=json.dumps({"error": str(e)}, ensure_ascii=False))]

async def get_stock_history(stock_code: str, start_date: str = None, end_date: str = None, frequency: str = "d"):
    end_date = end_date or datetime.now().strftime('%Y-%m-%d')
    start_date = start_date or (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    login_baostock()
    try:
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,volume,amount,pctChg",
            start_date=start_date,
            end_date=end_date,
            frequency=frequency,
            adjustflag="3"
        )
        
        if rs.error_code != '0':
            return [types.TextContent(type="text", text=json.dumps({"error": rs.error_msg}, ensure_ascii=False))]
        
        data = []
        while rs.next():
            row = rs.get_row_data()
            data.append({
                "date": row[0],
                "code": row[1],
                "open": float(row[2]) if row[2] else 0.0,
                "high": float(row[3]) if row[3] else 0.0,
                "low": float(row[4]) if row[4] else 0.0,
                "close": float(row[5]) if row[5] else 0.0,
                "volume": int(row[6]) if row[6] else 0,
                "amount": float(row[7]) if row[7] else 0.0,
                "pct_chg": float(row[8]) if row[8] else 0.0
            })
        
        return [types.TextContent(type="text", text=json.dumps({"data": data}, ensure_ascii=False, indent=2))]
    finally:
        logout_baostock()

async def get_stock_info(stock_code: str):
    login_baostock()
    try:
        rs = bs.query_stock_basic(code=stock_code)
        if rs.error_code != '0':
            return [types.TextContent(type="text", text=json.dumps({"error": rs.error_msg}, ensure_ascii=False))]
        
        data = []
        while rs.next():
            data.append({
