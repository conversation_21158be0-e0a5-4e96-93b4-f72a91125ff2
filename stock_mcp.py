#!/usr/bin/env python3
"""
Baostock股票信息查询MCP服务器
基于官方MCP协议实现
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
import baostock as bs
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
import mcp.server.stdio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建服务器实例
server = Server("baostock-mcp")

def login_baostock():
    """登录Baostock"""
    try:
        lg = bs.login()
        if lg.error_code != '0':
            raise Exception(f"登录失败: {lg.error_msg}")
        logger.info("Baostock登录成功")
        return True
    except Exception as e:
        logger.error(f"Baostock登录错误: {e}")
        raise

def logout_baostock():
    """登出Baostock"""
    try:
        bs.logout()
        logger.info("Baostock登出成功")
    except:
        pass

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """列出可用的工具"""
    return [
        types.Tool(
            name="get_stock_history",
            description="获取股票历史K线数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_code": {
                        "type": "string",
                        "description": "股票代码，如 'sh.600000'"
                    },
                    "start_date": {
                        "type": "string",
                        "description": "开始日期，格式: YYYY-MM-DD"
                    },
                    "end_date": {
                        "type": "string",
                        "description": "结束日期，格式: YYYY-MM-DD"
                    },
                    "frequency": {
                        "type": "string",
                        "description": "数据频率: 'd'=日线, 'w'=周线, 'm'=月线",
                        "default": "d"
                    }
                },
                "required": ["stock_code"]
            }
        ),
        types.Tool(
            name="get_stock_info",
            description="获取股票基本信息",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_code": {
                        "type": "string",
                        "description": "股票代码，如 'sh.600000'"
                    }
                },
                "required": ["stock_code"]
            }
        ),
        types.Tool(
            name="search_stocks",
            description="搜索股票",
            inputSchema={
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词，可以是股票代码或名称"
                    }
                },
                "required": ["keyword"]
            }
        ),
        types.Tool(
            name="get_financial_data",
            description="获取股票财务数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_code": {
                        "type": "string",
                        "description": "股票代码，如 'sh.600000'"
                    }
                },
                "required": ["stock_code"]
            }
        ),
        types.Tool(
            name="get_realtime_quotes",
            description="获取股票实时行情",
            inputSchema={
                "type": "object",
                "properties": {
                    "stock_code": {
                        "type": "string",
                        "description": "股票代码，如 'sh.600000'"
                    }
                },
                "required": ["stock_code"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> list[types.TextContent]:
    """处理工具调用"""
    if arguments is None:
        arguments = {}
    
    try:
        if name == "get_stock_history":
            return await get_stock_history(**arguments)
        elif name == "get_stock_info":
            return await get_stock_info(**arguments)
        elif name == "search_stocks":
            return await search_stocks(**arguments)
        elif name == "get_financial_data":
            return await get_financial_data(**arguments)
        elif name == "get_realtime_quotes":
            return await get_realtime_quotes(**arguments)
        else:
            raise ValueError(f"未知工具: {name}")
    except Exception as e:
        logger.error(f"工具调用错误: {e}")
        return [types.TextContent(type="text", text=json.dumps({"error": str(e)}, ensure_ascii=False))]

async def get_stock_history(stock_code: str, start_date: str = None, end_date: str = None, frequency: str = "
