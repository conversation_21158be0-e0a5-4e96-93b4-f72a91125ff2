#!/usr/bin/env python3
"""
基于Baostock的股票信息查询MCP服务器
提供股票历史数据、实时行情、财务数据等查询功能
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import baostock as bs

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StockQueryParams:
    """股票查询参数"""
    def __init__(self, stock_code: str, start_date: str = None, end_date: str = None, frequency: str = "d"):
        self.stock_code = stock_code
        self.start_date = start_date
        self.end_date = end_date
        self.frequency = frequency

class StockInfoParams:
    """股票信息查询参数"""
    def __init__(self, stock_code: str):
        self.stock_code = stock_code

class StockSearchParams:
    """股票搜索参数"""
    def __init__(self, keyword: str):
        self.keyword = keyword

class BaostockMCP:
    """Baostock MCP服务器类"""
    
    def __init__(self):
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def _login_baostock(self):
        """登录Baostock"""
        try:
            lg = bs.login()
            if lg.error_code != '0':
                raise Exception(f"登录失败: {lg.error_msg}")
            logger.info("Baostock登录成功")
            return True
        except Exception as e:
            logger.error(f"Baostock登录错误: {e}")
            raise
    
    def _logout_baostock(self):
        """登出Baostock"""
        try:
            bs.logout()
            logger.info("Baostock登出成功")
        except:
            pass
    
    def get_stock_history(self, stock_code: str, start_date: str = None, end_date: str = None, frequency: str = "d") -> str:
        """获取股票历史数据"""
        
        # 设置默认日期
        end_date = end_date or datetime.now().strftime('%Y-%m-%d')
        start_date = start_date or (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        self._login_baostock()
        
        try:
            rs = bs.query_history_k_data_plus(
                stock_code,
                "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
                start_date=start_date,
                end_date=end_date,
                frequency=frequency,
                adjustflag="3"
            )
            
            if rs.error_code != '0':
                return json.dumps({"error": f"查询失败: {rs.error_msg}"}, ensure_ascii=False)
            
            data = []
            while rs.next():
                row = rs.get_row_data()
                data.append({
                    "date": row[0],
                    "code": row[1],
                    "open": float(row[2]) if row[2] else 0.0,
                    "high": float(row[3]) if row[3] else 0.0,
                    "low": float(row[4]) if row[4] else 0.0,
                    "close": float(row[5]) if row[5] else 0.0,
                    "volume": int(row[7]) if row[7] else 0,
                    "amount": float(row[8]) if row[8] else 0.0,
                    "pct_chg": float(row[12]) if row[12] else 0.0
                })
            
            result = {
                "stock_code": stock_code,
                "data_count": len(data),
                "start_date": start_date,
                "end_date": end_date,
                "data": data
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        finally:
            self._logout_baostock()
    
    def get_stock_info(self, stock_code: str) -> str:
        """获取股票基本信息"""
        
        self._login_baostock()
        
        try:
            rs = bs.query_stock_basic(code=stock_code)
            
            if rs.error_code != '0':
                return json.dumps({"error": f"查询失败: {rs.error_msg}"}, ensure_ascii=False)
            
            data = []
            while rs.next():
                data.append({
                    "code": rs.get_row_data()[0],
                    "code_name": rs.get_row_data()[1],
                    "ipo_date": rs.get_row_data()[2],
                    "out_date": rs.get_row_data()[3],
                    "type": rs.get_row_data()[4],
                    "status": rs.get_row_data()[5]
                })
            
            result = {
                "stock_code": stock_code,
                "data": data
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        finally:
            self._logout_baostock()
    
    def search_stocks(self, keyword: str) -> str:
        """搜索股票"""
        
        self._login_baostock()
        
        try:
            rs = bs.query_stock_basic(code_name=keyword)
            
            if rs.error_code != '0':
                return json.dumps({"error": f"查询失败: {rs.error_msg}"}, ensure_ascii=False)
            
            data = []
            while rs.next():
                data.append({
                    "code": rs.get_row_data()[0],
                    "code_name": rs.get_row_data()[1],
                    "ipo_date": rs.get_row_data()[2],
                    "out_date": rs.get_row_data()[3],
                    "type": rs.get_row_data()[4],
                    "status": rs.get_row_data()[5]
                })
            
            result = {
                "keyword": keyword,
                "data_count": len(data),
                "data": data
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        finally:
            self._logout_baostock()
    
    def get_financial_data(self, stock_code: str) -> str:
        """获取股票财务数据"""
        
        self._login_baostock()
        
        try:
            # 获取最新季度财务数据
            year = datetime.now().year
            quarter = (datetime.now().month - 1) // 3 + 1
            
            rs = bs.query_profit_data(code=stock_code, year=year, quarter=quarter)
            
            if rs.error_code != '0':
                return json.dumps({"error": f"查询失败: {rs.error_msg}"}, ensure_ascii=False)
            
            data = []
            while rs.next():
                row = rs.get_row_data()
                data.append({
                    "code": row[0],
                    "pub_date": row[1],
                    "stat_date": row[2],
                    "np_parent_company_owners": float(row[3]) if row[3] else 0.0,
                    "net_profit": float(row[4]) if row[4] else 0.0,
                    "eps": float(row[5]) if row[5] else 0.0,
                    "roe": float(row[6]) if row[6] else 0.0
                })
            
            result = {
                "stock_code": stock_code,
                "year": year,
                "quarter": quarter,
                "data": data
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        finally:
            self._logout_baostock()
    
    def get_realtime_quotes(self, stock_code: str) -> str:
        """获取股票实时行情"""
        
        self._login_baostock()
        
        try:
            rs = bs.query_real_time_data(code=stock_code)
            
            if rs.error_code != '0':
                return json.dumps({"error": f"查询失败: {rs.error_msg}"}, ensure_ascii=False)
            
            data = []
            while rs.next():
                row = rs.get_row_data()
                data.append({
                    "code": row[0],
                    "time": row[1],
                    "open": float(row[2]) if row[2] else 0.0,
                    "high": float(row[3]) if row[3] else 0.0,
                    "low": float(row[4]) if row[4] else 0.0,
                    "close": float(row[5]) if row[5] else 0.0,
                    "volume": int(row[6]) if row[6] else 0,
                    "amount": float(row[7]) if row[7] else 0.0,
                    "preclose": float(row[8]) if row[8] else 0.0
                })
            
            result = {
                "stock_code": stock_code,
                "data": data
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        finally:
            self._logout_baostock()

class SimpleMCPServer:
    """简单的MCP服务器实现"""
    
    def __init__(self):
        self.baostock_mcp = BaostockMCP()
        self.running = True
    
    def process_command(self, command: str, args: dict) -> str:
        """处理命令"""
        try:
            if command == "get_stock_history":
                return self.baostock_mcp.get_stock_history(**args)
            elif command == "get_stock_info":
                return self.baostock_mcp.get_stock_info(**args)
            elif command == "search_stocks":
                return self.baostock_mcp.search_stocks(**args)
            elif command == "get_financial_data":
                return self.baostock_mcp.get_financial_data(**args)
            elif command == "get_realtime_quotes":
                return self.baostock_mcp.get_realtime_quotes(**args)
            else:
                return json.dumps({"error": f"未知命令: {command}"}, ensure_ascii=False)
        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)
    
    def start_interactive_mode(self):
        """启动交互模式"""
        print("🚀 Baostock股票信息查询MCP服务器")
        print("可用的命令:")
        print("1. get_stock_history - 获取股票历史数据")
        print("2. get_stock_info - 获取股票基本信息")
        print("3. search_stocks - 搜索股票")
        print("4. get_financial_data - 获取财务数据")
        print("5. get_realtime_quotes - 获取实时行情")
        print("6. exit - 退出")
        print()
        
        while self.running:
            try:
                command = input("请输入命令 (或 'help' 查看帮助): ").strip()
                
                if command == "exit" or command == "quit":
                    self.running = False
                    print("感谢使用，再见！")
                    break
                
                elif command == "help":
                    print("\n使用示例:")
                    print("get_stock_history stock_code=sh.600000 start_date=2024-01-01 end_date=2024-12-31")
                    print("get_stock_info stock_code=sh.600000")
                    print("search_stocks keyword=浦发银行")
                    print("get_financial_data stock_code=sh.600000")
                    print("get_realtime_quotes stock_code=sh.600000")
                    print()
                    continue
                
                elif command in ["get_stock_history", "get_stock_info", "search_stocks", 
                               "get_financial_data", "get_realtime_quotes"]:
                    
                    args = {}
                    print(f"正在执行: {command}")
                    
                    if command == "get_stock_history":
                        stock_code = input("股票代码 (如 sh.600000): ").strip()
                        start_date = input("开始日期 (YYYY-MM-DD, 可选): ").strip()
                        end_date = input("结束日期 (YYYY-MM-DD, 可选): ").strip()
                        frequency = input("频率 (d/w/m, 默认d): ").strip() or "d"
                        
                        args["stock_code"] = stock_code
                        if start_date:
                            args["start_date"] = start_date
                        if end_date:
                            args["end_date"] = end_date
                        args["frequency"] = frequency
                    
                    elif command in ["get_stock_info", "get_financial_data", "get_realtime_quotes"]:
                        stock_code = input("股票代码 (如 sh.600000): ").strip()
                        args["stock_code"] = stock_code
                    
                    elif command == "search_stocks":
                        keyword = input("搜索关键词: ").strip()
                        args["keyword"] = keyword
                    
                    result = self.process_command(command, args)
                    print("\n结果:")
                    print(result)
                    print()
                
                else:
                    print("未知命令，请输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                print("\n感谢使用，再见！")
                break
            except Exception as e:
                print(f"错误: {e}")

def main():
    """主函数"""
    server = SimpleMCPServer()
    
    # 检查是否有命令行参数
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        server.start_interactive_mode()
    else:
        print("Baostock MCP服务器已就绪")
        print("使用 --interactive 参数启动交互模式")
        print("示例: python3 stock_mcp_server.py --interactive")

if __name__ == "__main__":
    main()